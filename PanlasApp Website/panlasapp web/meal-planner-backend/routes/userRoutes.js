const express = require('express');
const router = express.Router();
const userController = require('../controllers/userController');
const auth = require('../middleware/auth'); // Make sure this path is correct
const { getRecentlyViewedMeals } = require('../controllers/userController');
// userController.register should be properly implemented

// User registration route
router.post('/signup', userController.register);

// Login route
router.post('/login', userController.login);

// Protected routes
router.get('/profile', auth, userController.getProfile);
router.put('/profile', auth, userController.updateProfile);
router.put('/change-password', auth, userController.changePassword);
// router.delete('/account', auth, userController.deleteAccount);

// New routes for dietary preferences
router.get('/dietary-preferences', auth, userController.getDietaryPreferences);
router.put('/dietary-preferences', auth, userController.updateDietaryPreferences);

// Routes for favorite meals
router.get('/favorite-meals', auth, userController.getFavoriteMeals);
router.post('/favorite-meals', auth, userController.addFavoriteMeal);
router.delete('/favorite-meals/:mealId', auth, userController.removeFavoriteMeal);

// Routes for saved meal plan templates
router.get('/saved-meal-plans', auth, userController.getSavedMealPlanTemplates);
router.post('/saved-meal-plans', auth, userController.saveMealPlanTemplate);
router.delete('/saved-meal-plans/:templateId', auth, userController.deleteSavedMealPlanTemplate);


router.get('/recently-viewed-meals', auth, userController.getRecentlyViewedMeals);
router.post('/recently-viewed-meals', auth, userController.addRecentlyViewedMeal);

// Recently added to meal plans routes
router.get('/recently-added-to-meal-plans', auth, userController.getRecentlyAddedToMealPlans);
router.post('/recently-added-to-meal-plans', auth, userController.addRecentlyAddedToMealPlan);

// Get meals from saved meal plans for history
router.get('/meals-from-saved-plans', auth, userController.getMealsFromSavedPlans);

router.get('/family-members', auth, userController.getFamilyMembers);
router.post('/family-members', auth, userController.addFamilyMember);

router.delete('/family-members/:memberId', auth, userController.removeFamilyMember);


module.exports = router;
