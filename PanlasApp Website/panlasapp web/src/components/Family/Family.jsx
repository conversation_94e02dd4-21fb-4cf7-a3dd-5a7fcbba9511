import React, { useState, useEffect } from "react";
import Layout from "../Layout/Layout";
import axios from "axios";
import "../../../src/App.css";

const Family = () => {
  const [user, setUser] = useState(null);
  const [userPrefs, setUserPrefs] = useState({
    dietaryPreferences: "",
    allergies: "",
  });
  const [userPrefsSaved, setUserPrefsSaved] = useState(false);
  const [members, setMembers] = useState([]);
  const [showForm, setShowForm] = useState(false);
  const [form, setForm] = useState({
    name: "",
    dietaryPreferences: "",
    allergies: "",
    dislikedIngredients: "",
  });

  // Fetch logged-in user info
  useEffect(() => {
    const fetchUser = async () => {
      try {
        const token = localStorage.getItem("token");
        if (!token) return;
        const res = await axios.get("http://localhost:5000/api/users/profile", {
          headers: { Authorization: `Bearer ${token}` },
        });
        setUser(res.data);
        setUserPrefs({
          dietaryPreferences: res.data.dietaryPreferences || "",
          allergies: res.data.allergies || "",
        });
      } catch (err) {
        setUser(null);
      }
    };
    fetchUser();
  }, []);

  const handleUserPrefsChange = (e) => {
    setUserPrefs({ ...userPrefs, [e.target.name]: e.target.value });
    setUserPrefsSaved(false);
  };

const handleUserPrefsSave = async (e) => {
  e.preventDefault();
  try {
    const token = localStorage.getItem("token");
    if (!token) return;
    const dietaryPreferences = {
      restrictions: userPrefs.dietaryPreferences
        .split(",")
        .map(s => s.trim())
        .filter(Boolean),
      allergies: userPrefs.allergies
        .split(",")
        .map(s => s.trim())
        .filter(Boolean),
      dislikedIngredients: userPrefs.dislikedIngredients
        ? userPrefs.dislikedIngredients.split(",").map(s => s.trim()).filter(Boolean)
        : [],
    };
    await axios.put(
      "http://localhost:5000/api/users/dietary-preferences",
      dietaryPreferences,
      { headers: { Authorization: `Bearer ${token}` } }
    );
    setUserPrefsSaved(true);
  } catch (err) {
    setUserPrefsSaved(false);
    alert("Failed to save preferences.");
  }
};

  const handleInputChange = (e) => {
    setForm({ ...form, [e.target.name]: e.target.value });
  };

const handleAddMember = async (e) => {
  e.preventDefault();
  const token = localStorage.getItem("token");
  if (!token) return;
  try {
    const dietaryPreferences = {
      restrictions: form.dietaryPreferences
        .split(",")
        .map(s => s.trim())
        .filter(Boolean),
      allergies: form.allergies
        .split(",")
        .map(s => s.trim())
        .filter(Boolean),
      dislikedIngredients: form.dislikedIngredients
        ? form.dislikedIngredients.split(",").map(s => s.trim()).filter(Boolean)
        : [],
    };
    const res = await axios.post(
      "http://localhost:5000/api/users/family-members",
      {
        name: form.name,
        dietaryPreferences,
      },
      { headers: { Authorization: `Bearer ${token}` } }
    );
    setMembers(res.data.familyMembers);
    setForm({ name: "", dietaryPreferences: "", allergies: "", dislikedIngredients: "" });
    setShowForm(false);
  } catch (err) {
    alert("Failed to add family member.");
  }
};

const handleRemoveMember = async (memberId) => {
  const token = localStorage.getItem("token");
  if (!token) return;
  try {
    const res = await axios.delete(
      `http://localhost:5000/api/users/family-members/${memberId}`,
      { headers: { Authorization: `Bearer ${token}` } }
    );
    setMembers(res.data.familyMembers);
  } catch (err) {
    alert("Failed to remove family member.");
  }
};

useEffect(() => {
  // Fetch family members from backend
  const fetchFamilyMembers = async () => {
    const token = localStorage.getItem("token");
    if (!token) return;
    try {
      const res = await axios.get(
        "http://localhost:5000/api/users/family-members",
        { headers: { Authorization: `Bearer ${token}` } }
      );
      setMembers(res.data.familyMembers);
    } catch (err) {
      setMembers([]);
    }
  };
  fetchFamilyMembers();
}, []);

  return (
    <Layout>
      <div className="main-content">
        <div className="family-container-modern">
          <div className="family-header-modern">
            <div className="family-title-section">
              <h1>Family Profile</h1>
              <p className="family-subtitle">Manage your family's dietary preferences and meal planning</p>
            </div>
            {user && (
              <div className="current-user-card">
                <div className="user-avatar">
                  {user.firstName?.charAt(0)}{user.lastName?.charAt(0)}
                </div>
                <div className="user-info">
                  <span className="user-name">{user.firstName} {user.lastName}</span>
                  <span className="user-role">Family Admin</span>
                </div>
              </div>
            )}
          </div>

          <div className="user-prefs-card">
            <div className="card-header">
              <h2>Your Dietary Profile</h2>
              <p className="card-subtitle">Set your personal dietary preferences and restrictions</p>
            </div>
            <form className="modern-form" onSubmit={handleUserPrefsSave}>
              <div className="form-grid">
                <div className="form-group">
                  <label className="form-label">
                    <span className="label-text">Dietary Preferences</span>
                    <input
                      type="text"
                      name="dietaryPreferences"
                      value={userPrefs.dietaryPreferences}
                      onChange={handleUserPrefsChange}
                      placeholder="e.g. vegetarian, halal, keto"
                      className="form-input"
                    />
                  </label>
                </div>
                <div className="form-group">
                  <label className="form-label">
                    <span className="label-text">Allergies</span>
                    <input
                      type="text"
                      name="allergies"
                      value={userPrefs.allergies}
                      onChange={handleUserPrefsChange}
                      placeholder="e.g. peanuts, shellfish, dairy"
                      className="form-input"
                    />
                  </label>
                </div>
                <div className="form-group">
                  <label className="form-label">
                    <span className="label-text">Disliked Ingredients</span>
                    <input
                      type="text"
                      name="dislikedIngredients"
                      value={userPrefs.dislikedIngredients || ""}
                      onChange={handleUserPrefsChange}
                      placeholder="e.g. eggplant, okra, mushrooms"
                      className="form-input"
                    />
                  </label>
                </div>
              </div>
              <div className="form-actions">
                <button type="submit" className="btn-primary-modern">
                  Save Preferences
                </button>
                {userPrefsSaved && (
                  <span className="success-message">
                    ✓ Preferences saved successfully!
                  </span>
                )}
              </div>
            </form>
          </div>

          <div className="family-members-section">
            <div className="section-header">
              <div>
                <h2>Family Members</h2>
                <p className="section-subtitle">Manage dietary preferences for your family</p>
              </div>
              <button
                className="add-member-btn-modern"
                onClick={() => setShowForm((prev) => !prev)}
              >
                {showForm ? (
                  <>
                    <span>✕</span>
                    Cancel
                  </>
                ) : (
                  <>
                    <span>+</span>
                    Add Member
                  </>
                )}
              </button>
            </div>

            {showForm && (
              <div className="add-member-card">
                <div className="card-header">
                  <h3>Add New Family Member</h3>
                  <p className="card-subtitle">Enter their dietary information</p>
                </div>
                <form className="modern-form" onSubmit={handleAddMember}>
                  <div className="form-grid">
                    <div className="form-group">
                      <label className="form-label">
                        <span className="label-text">Name *</span>
                        <input
                          type="text"
                          name="name"
                          value={form.name}
                          onChange={handleInputChange}
                          required
                          className="form-input"
                          placeholder="Enter family member's name"
                        />
                      </label>
                    </div>
                    <div className="form-group">
                      <label className="form-label">
                        <span className="label-text">Dietary Preferences</span>
                        <input
                          type="text"
                          name="dietaryPreferences"
                          value={form.dietaryPreferences}
                          onChange={handleInputChange}
                          placeholder="e.g. vegetarian, halal, keto"
                          className="form-input"
                        />
                      </label>
                    </div>
                    <div className="form-group">
                      <label className="form-label">
                        <span className="label-text">Allergies</span>
                        <input
                          type="text"
                          name="allergies"
                          value={form.allergies}
                          onChange={handleInputChange}
                          placeholder="e.g. peanuts, shellfish, dairy"
                          className="form-input"
                        />
                      </label>
                    </div>
                    <div className="form-group">
                      <label className="form-label">
                        <span className="label-text">Disliked Ingredients</span>
                        <input
                          type="text"
                          name="dislikedIngredients"
                          value={form.dislikedIngredients || ""}
                          onChange={handleInputChange}
                          placeholder="e.g. eggplant, okra, mushrooms"
                          className="form-input"
                        />
                      </label>
                    </div>
                  </div>
                  <div className="form-actions">
                    <button type="submit" className="btn-primary-modern">
                      Add Family Member
                    </button>
                  </div>
                </form>
              </div>
            )}

            {members.length === 0 ? (
              <div className="empty-state">
                <div className="empty-state-icon">👥</div>
                <h3>No family members yet</h3>
                <p>Add family members to manage their dietary preferences and create personalized meal plans.</p>
                <button
                  className="btn-primary-modern"
                  onClick={() => setShowForm(true)}
                >
                  Add Your First Member
                </button>
              </div>
            ) : (
              <div className="family-members-grid">
                {members.map((member, idx) => (
                  <div key={member._id || idx} className="member-card">
                    <div className="member-header">
                      <div className="member-avatar" style={{
                        background: `linear-gradient(135deg, ${
                          ['#10b981', '#3b82f6', '#8b5cf6', '#f59e0b', '#ef4444', '#06b6d4'][idx % 6]
                        } 0%, ${
                          ['#059669', '#1d4ed8', '#7c3aed', '#d97706', '#dc2626', '#0891b2'][idx % 6]
                        } 100%)`
                      }}>
                        {member.name?.charAt(0)?.toUpperCase()}
                      </div>
                      <div className="member-info">
                        <h3 className="member-name">{member.name}</h3>
                        <span className="member-role">Family Member</span>
                      </div>
                      <button
                        className="remove-btn"
                        onClick={() => handleRemoveMember(member._id)}
                        title="Remove member"
                      >
                        ✕
                      </button>
                    </div>
                    <div className="member-details">
                      <div className="detail-item">
                        <span className="detail-label">Dietary Preferences</span>
                        <span className="detail-value">
                          {member.dietaryPreferences?.restrictions?.length
                            ? member.dietaryPreferences.restrictions.join(", ")
                            : "None specified"}
                        </span>
                      </div>
                      <div className="detail-item">
                        <span className="detail-label">Allergies</span>
                        <span className="detail-value">
                          {member.dietaryPreferences?.allergies?.length
                            ? member.dietaryPreferences.allergies.join(", ")
                            : "None specified"}
                        </span>
                      </div>
                      <div className="detail-item">
                        <span className="detail-label">Disliked Ingredients</span>
                        <span className="detail-value">
                          {member.dietaryPreferences?.dislikedIngredients?.length
                            ? member.dietaryPreferences.dislikedIngredients.join(", ")
                            : "None specified"}
                        </span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default Family;
